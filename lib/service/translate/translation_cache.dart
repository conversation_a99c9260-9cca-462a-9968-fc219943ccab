import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:dasso_reader/service/translate/index.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Translation cache entry
class TranslationCacheEntry {
  final String originalText;
  final String translatedText;
  final String fromLanguage;
  final String toLanguage;
  final TranslateService service;
  final DateTime timestamp;

  const TranslationCacheEntry({
    required this.originalText,
    required this.translatedText,
    required this.fromLanguage,
    required this.toLanguage,
    required this.service,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
        'originalText': originalText,
        'translatedText': translatedText,
        'fromLanguage': fromLanguage,
        'toLanguage': toLanguage,
        'service': service.name,
        'timestamp': timestamp.millisecondsSinceEpoch,
      };

  factory TranslationCacheEntry.fromJson(Map<String, dynamic> json) =>
      TranslationCacheEntry(
        originalText: json['originalText'] as String,
        translatedText: json['translatedText'] as String,
        fromLanguage: json['fromLanguage'] as String,
        toLanguage: json['toLanguage'] as String,
        service: getTranslateService(json['service'] as String),
        timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int),
      );

  /// Generate a unique cache key for this translation request
  String get cacheKey => _generateCacheKey(
        originalText,
        fromLanguage,
        toLanguage,
        service,
      );

  static String _generateCacheKey(
    String text,
    String fromLang,
    String toLang,
    TranslateService service,
  ) {
    final input = '$text|$fromLang|$toLang|${service.name}';
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}

/// In-memory translation cache with LRU eviction
class TranslationCache {
  static const int _maxCacheSize = 1000;
  static const Duration _cacheExpiry = Duration(hours: 24);

  final Map<String, TranslationCacheEntry> _cache = {};
  final List<String> _accessOrder = [];

  static final TranslationCache _instance = TranslationCache._internal();
  factory TranslationCache() => _instance;
  TranslationCache._internal();

  /// Get cached translation if available and not expired
  String? getCachedTranslation(
    String text,
    LangList from,
    LangList to,
    TranslateService service,
  ) {
    final key = TranslationCacheEntry._generateCacheKey(
      text,
      from.code,
      to.code,
      service,
    );

    final entry = _cache[key];
    if (entry == null) {
      return null;
    }

    // Check if entry is expired
    if (DateTime.now().difference(entry.timestamp) > _cacheExpiry) {
      _cache.remove(key);
      _accessOrder.remove(key);
      AnxLog.info('Translation cache entry expired and removed: $key');
      return null;
    }

    // Update access order for LRU
    _accessOrder.remove(key);
    _accessOrder.add(key);

    AnxLog.info('Translation cache hit: ${text.length} chars');
    return entry.translatedText;
  }

  /// Cache a translation result
  void cacheTranslation(
    String originalText,
    String translatedText,
    LangList from,
    LangList to,
    TranslateService service,
  ) {
    if (originalText.isEmpty || translatedText.isEmpty) {
      return;
    }

    final entry = TranslationCacheEntry(
      originalText: originalText,
      translatedText: translatedText,
      fromLanguage: from.code,
      toLanguage: to.code,
      service: service,
      timestamp: DateTime.now(),
    );

    final key = entry.cacheKey;

    // Remove if already exists to update access order
    if (_cache.containsKey(key)) {
      _accessOrder.remove(key);
    }

    // Add to cache
    _cache[key] = entry;
    _accessOrder.add(key);

    // Evict oldest entries if cache is full
    while (_cache.length > _maxCacheSize) {
      final oldestKey = _accessOrder.removeAt(0);
      _cache.remove(oldestKey);
      AnxLog.info('Translation cache evicted oldest entry: $oldestKey');
    }

    AnxLog.info('Translation cached: ${originalText.length} chars -> ${translatedText.length} chars');
  }

  /// Clear all cached translations
  void clearCache() {
    final count = _cache.length;
    _cache.clear();
    _accessOrder.clear();
    AnxLog.info('Translation cache cleared: $count entries removed');
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    final now = DateTime.now();
    int expiredCount = 0;
    int totalSize = 0;

    for (final entry in _cache.values) {
      if (now.difference(entry.timestamp) > _cacheExpiry) {
        expiredCount++;
      }
      totalSize += entry.originalText.length + entry.translatedText.length;
    }

    return {
      'totalEntries': _cache.length,
      'expiredEntries': expiredCount,
      'maxSize': _maxCacheSize,
      'approximateMemoryUsage': totalSize,
      'cacheHitRate': 0.0, // Would need to track hits/misses for this
    };
  }

  /// Clean up expired entries
  void cleanupExpiredEntries() {
    final now = DateTime.now();
    final expiredKeys = <String>[];

    for (final entry in _cache.entries) {
      if (now.difference(entry.value.timestamp) > _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _cache.remove(key);
      _accessOrder.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      AnxLog.info('Translation cache cleanup: ${expiredKeys.length} expired entries removed');
    }
  }
}
