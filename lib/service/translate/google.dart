import 'package:dasso_reader/service/translate/index.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dio/dio.dart';

const urlGoogle = 'https://translate.google.com/translate_a/single';

Future<String> googleTranslateService(
  String text,
  LangList from,
  LangList to,
) async {
  final params = {
    'client': 'gtx',
    'sl': from.code,
    'tl': to.code,
    'dt': 't',
    'q': text,
  };
  final uri = Uri.parse(urlGoogle).replace(queryParameters: params);
  try {
    final response = await Dio().get<dynamic>(uri.toString());
    final result = response.data;

    // Validate response structure
    if (result == null ||
        result is! List ||
        result.isEmpty ||
        result[0] is! List ||
        (result[0] as List).isEmpty ||
        (result[0] as List)[0] is! List ||
        ((result[0] as List)[0] as List).isEmpty) {
      throw const TranslationException(
        'Invalid response format from Google Translate',
        TranslationErrorType.serviceUnavailable,
      );
    }

    final translatedText = (result[0][0][0] as String?) ?? '';
    if (translatedText.isEmpty) {
      throw const TranslationException(
        'Empty translation result from Google Translate',
        TranslationErrorType.serviceUnavailable,
      );
    }

    return translatedText;
  } on TranslationException {
    rethrow; // Re-throw our custom exceptions
  } catch (e) {
    AnxLog.severe('Google Translate Error: uri=$uri, error=$e');
    throw TranslationException(
      'Google Translate service error: ${e.toString()}',
      TranslationErrorType.serviceUnavailable,
      e is Exception ? e : Exception(e.toString()),
    );
  }
}
