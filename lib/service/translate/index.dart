import 'dart:core';
import 'dart:io';

import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/service/translate/google.dart';
import 'package:dasso_reader/service/translate/microsoft.dart';
import 'package:dasso_reader/service/translate/translation_cache.dart';
import 'package:dasso_reader/service/translate/service_status.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

/// Translation error types for better error handling
class TranslationException implements Exception {
  final String message;
  final TranslationErrorType type;
  final Exception? originalException;

  const TranslationException(
    this.message,
    this.type, [
    this.originalException,
  ]);

  @override
  String toString() => 'TranslationException: $message';
}

enum TranslationErrorType {
  networkError,
  serviceUnavailable,
  rateLimited,
  invalidInput,
  authenticationError,
  unknownError,
}

enum TranslateService {
  google('Google'),
  microsoft('Microsoft');
  // baidu('Baidu'),
  // tencent('Tencent'),
  // deepl('Deepl'),
  // openai('OpenAI');

  const TranslateService(this.label);

  final String label;
}

TranslateService getTranslateService(String name) {
  return TranslateService.values.firstWhere((e) => e.name == name);
}

enum LangList {
  auto('Auto', 'Auto'),
  english('en', 'English'),
  simplifiedChinese('zh-CN', '简体中文'),
  traditionalChinese('zh-TW', '繁體中文'),
  arabic('ar', 'العربية'),
  bulgarian('bg', 'Български'),
  catalan('ca', 'Català'),
  croatian('hr', 'Hrvatski'),
  czech('cs', 'Čeština'),
  danish('da', 'Dansk'),
  dutch('nl', 'Nederlands'),
  finnish('fi', 'Suomi'),
  french('fr', 'Français'),
  german('de', 'Deutsch'),
  greek('el', 'Ελληνικά'),
  hindi('hi', 'हिन्दी'),
  hungarian('hu', 'Magyar'),
  indonesian('id', 'Indonesia'),
  italian('it', 'Italiano'),
  japanese('ja', '日本語'),
  korean('ko', '한국어'),
  malay('ms', 'Melayu'),
  maltese('mt', 'Malti'),
  norwegian('nb', 'Norsk Bokmål'),
  polish('pl', 'Polski'),
  portuguese('pt', 'Português'),
  romanian('ro', 'Română'),
  russian('ru', 'Русский'),
  slovak('sk', 'Slovenčina'),
  slovenian('sl', 'Slovenščina'),
  spanish('es', 'Español'),
  swedish('sv', 'Svenska'),
  tamil('ta', 'தமிழ்'),
  telugu('te', 'తెలుగు'),
  thai('th', 'ไทย'),
  turkish('tr', 'Türkçe'),
  ukrainian('uk', 'Українська'),
  vietnamese('vi', 'Tiếng Việt');

  const LangList(this.code, this.nativeName);

  final String code;
  final String nativeName;

  String getNative(BuildContext context) => this == LangList.auto
      ? L10n.of(context).settings_translate_auto
      : nativeName;
}

LangList getLang(String code) {
  if (code == 'auto') return LangList.auto;

  return LangList.values
      .firstWhere((e) => e.code == code, orElse: () => LangList.english);
}

Future<String> translateText(String text, {TranslateService? service}) async {
  // Input validation
  if (text.trim().isEmpty) {
    throw const TranslationException(
      'Text cannot be empty',
      TranslationErrorType.invalidInput,
    );
  }

  service ??= Prefs().translateService;
  final from = Prefs().translateFrom;
  final to = Prefs().translateTo;

  // Validate language settings
  if (from == to && from != LangList.auto) {
    AnxLog.warning('Source and target languages are the same: ${from.code}');
    return text; // Return original text if same language
  }

  // Check cache first
  final cache = TranslationCache();
  final cachedResult = cache.getCachedTranslation(text, from, to, service);
  if (cachedResult != null) {
    return cachedResult;
  }

  try {
    String result;
    switch (service) {
      case TranslateService.google:
        result = await googleTranslateService(text, from, to);
        break;
      case TranslateService.microsoft:
        result = await microsoftTranslateService(text, from, to);
        break;
      // case TranslateService.baidu:
      // result = await TranslateApi().baidu(text);
      // break;
      // case TranslateService.tencent:
      // result = await TranslateApi().tencent(text);
      // break;
      // case TranslateService.deepl:
      // result = await TranslateApi().deepl(text);
      // break;
      // case TranslateService.openai:
      // result = await TranslateApi().openai(text);
      // break;
    }

    // Cache the successful result
    cache.cacheTranslation(text, result, from, to, service);
    return result;
  } on TranslationException {
    rethrow; // Re-throw our custom exceptions
  } on DioException catch (e) {
    AnxLog.severe('Translation network error: ${e.message}');
    throw _handleDioException(e);
  } catch (e) {
    AnxLog.severe('Translation unknown error: $e');
    throw TranslationException(
      'Translation failed: ${e.toString()}',
      TranslationErrorType.unknownError,
      e is Exception ? e : Exception(e.toString()),
    );
  }
}

/// Handle Dio exceptions and convert them to TranslationExceptions
TranslationException _handleDioException(DioException e) {
  switch (e.type) {
    case DioExceptionType.connectionTimeout:
    case DioExceptionType.sendTimeout:
    case DioExceptionType.receiveTimeout:
      return TranslationException(
        'Connection timeout. Please check your internet connection.',
        TranslationErrorType.networkError,
        e,
      );
    case DioExceptionType.badResponse:
      final statusCode = e.response?.statusCode;
      if (statusCode == 429) {
        return TranslationException(
          'Rate limit exceeded. Please try again later.',
          TranslationErrorType.rateLimited,
          e,
        );
      } else if (statusCode == 401 || statusCode == 403) {
        return TranslationException(
          'Authentication failed. Please check your service configuration.',
          TranslationErrorType.authenticationError,
          e,
        );
      } else if (statusCode != null && statusCode >= 500) {
        return TranslationException(
          'Translation service is temporarily unavailable.',
          TranslationErrorType.serviceUnavailable,
          e,
        );
      }
      return TranslationException(
        'Translation request failed (HTTP $statusCode).',
        TranslationErrorType.unknownError,
        e,
      );
    case DioExceptionType.cancel:
      return TranslationException(
        'Translation request was cancelled.',
        TranslationErrorType.unknownError,
        e,
      );
    case DioExceptionType.connectionError:
    case DioExceptionType.unknown:
    default:
      return TranslationException(
        'Network error. Please check your internet connection.',
        TranslationErrorType.networkError,
        e,
      );
  }
}

/// Get translation cache statistics
Map<String, dynamic> getTranslationCacheStats() {
  return TranslationCache().getCacheStats();
}

/// Clear translation cache
void clearTranslationCache() {
  TranslationCache().clearCache();
}

/// Clean up expired cache entries
void cleanupTranslationCache() {
  TranslationCache().cleanupExpiredEntries();
}

/// Translate text with automatic service fallback
Future<String> translateTextWithFallback(
  String text, {
  TranslateService? preferredService,
}) async {
  // Input validation
  if (text.trim().isEmpty) {
    throw const TranslationException(
      'Text cannot be empty',
      TranslationErrorType.invalidInput,
    );
  }

  final from = Prefs().translateFrom;
  final to = Prefs().translateTo;

  // Validate language settings
  if (from == to && from != LangList.auto) {
    AnxLog.warning('Source and target languages are the same: ${from.code}');
    return text; // Return original text if same language
  }

  // Check cache first
  final cache = TranslationCache();
  final statusManager = TranslationServiceStatusManager();

  // Try preferred service first, then fallback to best available
  final servicesToTry = <TranslateService>[];

  if (preferredService != null) {
    servicesToTry.add(preferredService);
  } else {
    servicesToTry.add(Prefs().translateService);
  }

  // Add fallback services
  final bestAvailable = statusManager.getBestAvailableService();
  if (bestAvailable != null && !servicesToTry.contains(bestAvailable)) {
    servicesToTry.add(bestAvailable);
  }

  // Add all other services as last resort
  for (final service in TranslateService.values) {
    if (!servicesToTry.contains(service)) {
      servicesToTry.add(service);
    }
  }

  TranslationException? lastException;

  for (final service in servicesToTry) {
    // Check cache for this specific service
    final cachedResult = cache.getCachedTranslation(text, from, to, service);
    if (cachedResult != null) {
      AnxLog.info('Using cached translation from ${service.name}');
      return cachedResult;
    }

    // Check service status
    final status = statusManager.getServiceStatus(service);
    if (status != null && status.isUnavailable) {
      AnxLog.info('Skipping unavailable service: ${service.name}');
      continue;
    }

    try {
      AnxLog.info('Attempting translation with ${service.name}');
      String result;

      switch (service) {
        case TranslateService.google:
          result = await googleTranslateService(text, from, to);
          break;
        case TranslateService.microsoft:
          result = await microsoftTranslateService(text, from, to);
          break;
      }

      // Cache the successful result
      cache.cacheTranslation(text, result, from, to, service);
      AnxLog.info('Translation successful with ${service.name}');
      return result;
    } on TranslationException catch (e) {
      lastException = e;
      AnxLog.warning('Translation failed with ${service.name}: ${e.message}');

      // Update service status based on the error
      await statusManager.checkServiceHealth(service);
      continue;
    } catch (e) {
      lastException = TranslationException(
        'Translation failed with ${service.name}: ${e.toString()}',
        TranslationErrorType.unknownError,
        e is Exception ? e : Exception(e.toString()),
      );
      AnxLog.severe('Translation error with ${service.name}: $e');
      continue;
    }
  }

  // If we get here, all services failed
  throw lastException ??
      const TranslationException(
        'All translation services are unavailable',
        TranslationErrorType.serviceUnavailable,
      );
}

/// Get translation service status information
Map<TranslateService, TranslationServiceStatus>
    getTranslationServiceStatuses() {
  return TranslationServiceStatusManager().getAllServiceStatuses();
}

/// Start translation service health monitoring
void startTranslationServiceMonitoring() {
  TranslationServiceStatusManager().startPeriodicHealthChecks();
}

/// Stop translation service health monitoring
void stopTranslationServiceMonitoring() {
  TranslationServiceStatusManager().stopPeriodicHealthChecks();
}
