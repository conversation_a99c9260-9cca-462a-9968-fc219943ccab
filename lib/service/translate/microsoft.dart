import 'dart:convert';

import 'package:dasso_reader/service/translate/index.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dio/dio.dart';

const urlMicrosoft =
    'https://api-edge.cognitive.microsofttranslator.com/translate';
const urlMicrosoftAuth = 'https://edge.microsoft.com/translate/auth';

Future<String> microsoftTranslateService(
  String text,
  LangList from,
  LangList to,
) async {
  final token = await getMicrosoftKey();
  final params = {
    'api-version': '3.0',
    'from': from == LangList.auto ? '' : from.code,
    'to': to.code,
  };
  final body = [
    {'Text': text},
  ];
  final uri = Uri.parse(urlMicrosoft).replace(queryParameters: params);
  final headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer $token',
  };
  try {
    final response = await <PERSON><PERSON>().post<dynamic>(
      uri.toString(),
      data: body,
      options: Options(headers: headers),
    );

    final result = response.data;

    // Validate response structure
    if (result == null ||
        result is! List ||
        result.isEmpty ||
        result[0] is! Map ||
        (result[0] as Map)['translations'] is! List ||
        ((result[0] as Map)['translations'] as List).isEmpty ||
        ((result[0] as Map)['translations'] as List)[0] is! Map ||
        (((result[0] as Map)['translations'] as List)[0] as Map)['text'] ==
            null) {
      throw const TranslationException(
        'Invalid response format from Microsoft Translator',
        TranslationErrorType.serviceUnavailable,
      );
    }

    final translatedText =
        (result[0]['translations'][0]['text'] as String?) ?? '';
    if (translatedText.isEmpty) {
      throw const TranslationException(
        'Empty translation result from Microsoft Translator',
        TranslationErrorType.serviceUnavailable,
      );
    }

    return translatedText;
  } on TranslationException {
    rethrow; // Re-throw our custom exceptions
  } catch (e) {
    AnxLog.severe('Microsoft Translate Error: uri=$uri, error=$e');
    throw TranslationException(
      'Microsoft Translator service error: ${e.toString()}',
      TranslationErrorType.serviceUnavailable,
      e is Exception ? e : Exception(e.toString()),
    );
  }
}

String microsoftKey = '';
num microsoftKeyExpired = 0;

Future<String> getMicrosoftKey() async {
  if (microsoftKey.isNotEmpty &&
      microsoftKeyExpired > DateTime.now().millisecondsSinceEpoch ~/ 1000) {
    return microsoftKey;
  }

  try {
    final response = await Dio().get<dynamic>(urlMicrosoftAuth);
    microsoftKey = response.data as String? ?? '';

    if (microsoftKey.isEmpty) {
      throw const TranslationException(
        'Failed to obtain Microsoft Translator authentication token',
        TranslationErrorType.authenticationError,
      );
    }

    // Parse JWT token
    final parts = microsoftKey.split('.');
    if (parts.length != 3) {
      throw const TranslationException(
        'Invalid Microsoft Translator authentication token format',
        TranslationErrorType.authenticationError,
      );
    }

    String jwt = parts[1];
    jwt = jwt.replaceAll('-', '+').replaceAll('_', '/');
    jwt = jwt.padRight(jwt.length + (4 - jwt.length % 4) % 4, '=');

    try {
      final jwtJson = jsonDecode(utf8.decode(base64Url.decode(jwt)))
          as Map<String, dynamic>;
      microsoftKeyExpired = (jwtJson['exp'] as num?) ?? 0;
    } catch (e) {
      AnxLog.warning('Failed to parse Microsoft token expiration: $e');
      // Set a default expiration of 1 hour from now
      microsoftKeyExpired =
          DateTime.now().millisecondsSinceEpoch ~/ 1000 + 3600;
    }

    return microsoftKey;
  } on TranslationException {
    rethrow;
  } catch (e) {
    AnxLog.severe('Microsoft authentication error: $e');
    throw TranslationException(
      'Failed to authenticate with Microsoft Translator: ${e.toString()}',
      TranslationErrorType.authenticationError,
      e is Exception ? e : Exception(e.toString()),
    );
  }
}
