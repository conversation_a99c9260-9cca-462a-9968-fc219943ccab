import 'dart:async';
import 'package:dasso_reader/service/translate/index.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dio/dio.dart';

/// Service status for translation providers
enum ServiceStatus {
  available,
  unavailable,
  degraded,
  unknown,
}

/// Translation service status information
class TranslationServiceStatus {
  final TranslateService service;
  final ServiceStatus status;
  final String? errorMessage;
  final DateTime lastChecked;
  final Duration? responseTime;

  const TranslationServiceStatus({
    required this.service,
    required this.status,
    this.errorMessage,
    required this.lastChecked,
    this.responseTime,
  });

  bool get isAvailable => status == ServiceStatus.available;
  bool get isDegraded => status == ServiceStatus.degraded;
  bool get isUnavailable => status == ServiceStatus.unavailable;

  @override
  String toString() =>
      'TranslationServiceStatus(${service.name}: $status${errorMessage != null ? ' - $errorMessage' : ''})';
}

/// Manages translation service status checking and fallback logic
class TranslationServiceStatusManager {
  static const Duration _statusCacheDuration = Duration(minutes: 5);
  static const Duration _healthCheckTimeout = Duration(seconds: 10);
  static const String _testText = 'Hello';

  final Map<TranslateService, TranslationServiceStatus> _statusCache = {};
  Timer? _periodicHealthCheck;

  static final TranslationServiceStatusManager _instance =
      TranslationServiceStatusManager._internal();
  factory TranslationServiceStatusManager() => _instance;
  TranslationServiceStatusManager._internal();

  /// Start periodic health checks for all services
  void startPeriodicHealthChecks() {
    _periodicHealthCheck?.cancel();
    _periodicHealthCheck = Timer.periodic(
      const Duration(minutes: 10),
      (_) => _checkAllServicesHealth(),
    );

    // Initial health check
    _checkAllServicesHealth();
  }

  /// Stop periodic health checks
  void stopPeriodicHealthChecks() {
    _periodicHealthCheck?.cancel();
    _periodicHealthCheck = null;
  }

  /// Get current status for a service
  TranslationServiceStatus? getServiceStatus(TranslateService service) {
    final status = _statusCache[service];
    if (status == null) return null;

    // Check if status is stale
    if (DateTime.now().difference(status.lastChecked) > _statusCacheDuration) {
      return null;
    }

    return status;
  }

  /// Check health of all available services
  Future<void> _checkAllServicesHealth() async {
    final futures =
        TranslateService.values.map((service) => checkServiceHealth(service));
    await Future.wait<TranslationServiceStatus>(futures);
  }

  /// Check health of a specific service
  Future<TranslationServiceStatus> checkServiceHealth(
      TranslateService service) async {
    final stopwatch = Stopwatch()..start();

    try {
      // Perform a simple translation test
      await _performHealthCheck(service);

      stopwatch.stop();
      final status = TranslationServiceStatus(
        service: service,
        status: ServiceStatus.available,
        lastChecked: DateTime.now(),
        responseTime: stopwatch.elapsed,
      );

      _statusCache[service] = status;
      AnxLog.info(
          'Service health check passed: ${service.name} (${stopwatch.elapsedMilliseconds}ms)');
      return status;
    } on TranslationException catch (e) {
      stopwatch.stop();
      final status = TranslationServiceStatus(
        service: service,
        status: _getStatusFromException(e),
        errorMessage: e.message,
        lastChecked: DateTime.now(),
        responseTime: stopwatch.elapsed,
      );

      _statusCache[service] = status;
      AnxLog.warning(
          'Service health check failed: ${service.name} - ${e.message}');
      return status;
    } catch (e) {
      stopwatch.stop();
      final status = TranslationServiceStatus(
        service: service,
        status: ServiceStatus.unavailable,
        errorMessage: e.toString(),
        lastChecked: DateTime.now(),
        responseTime: stopwatch.elapsed,
      );

      _statusCache[service] = status;
      AnxLog.severe('Service health check error: ${service.name} - $e');
      return status;
    }
  }

  /// Perform actual health check for a service
  Future<void> _performHealthCheck(TranslateService service) async {
    switch (service) {
      case TranslateService.google:
        await _checkGoogleService();
        break;
      case TranslateService.microsoft:
        await _checkMicrosoftService();
        break;
    }
  }

  /// Check Google Translate service health
  Future<void> _checkGoogleService() async {
    const url = 'https://translate.google.com/translate_a/single';
    final params = {
      'client': 'gtx',
      'sl': 'en',
      'tl': 'es',
      'dt': 't',
      'q': _testText,
    };

    final uri = Uri.parse(url).replace(queryParameters: params);
    final dio = Dio();
    dio.options.connectTimeout = _healthCheckTimeout;
    dio.options.receiveTimeout = _healthCheckTimeout;

    final response = await dio.get<dynamic>(uri.toString());

    if (response.statusCode != 200 || response.data == null) {
      throw const TranslationException(
        'Google Translate health check failed',
        TranslationErrorType.serviceUnavailable,
      );
    }
  }

  /// Check Microsoft Translator service health
  Future<void> _checkMicrosoftService() async {
    // First check if we can get an auth token
    const authUrl = 'https://edge.microsoft.com/translate/auth';
    final dio = Dio();
    dio.options.connectTimeout = _healthCheckTimeout;
    dio.options.receiveTimeout = _healthCheckTimeout;

    final authResponse = await dio.get<dynamic>(authUrl);
    if (authResponse.statusCode != 200 || authResponse.data == null) {
      throw const TranslationException(
        'Microsoft Translator auth failed',
        TranslationErrorType.authenticationError,
      );
    }

    // Then test a simple translation
    const translateUrl =
        'https://api-edge.cognitive.microsofttranslator.com/translate';
    final params = {
      'api-version': '3.0',
      'from': 'en',
      'to': 'es',
    };
    final body = [
      {'Text': _testText}
    ];
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${authResponse.data}',
    };

    final uri = Uri.parse(translateUrl).replace(queryParameters: params);
    final response = await dio.post<dynamic>(
      uri.toString(),
      data: body,
      options: Options(headers: headers),
    );

    if (response.statusCode != 200 || response.data == null) {
      throw const TranslationException(
        'Microsoft Translator health check failed',
        TranslationErrorType.serviceUnavailable,
      );
    }
  }

  /// Convert TranslationException to ServiceStatus
  ServiceStatus _getStatusFromException(TranslationException e) {
    switch (e.type) {
      case TranslationErrorType.networkError:
      case TranslationErrorType.serviceUnavailable:
        return ServiceStatus.unavailable;
      case TranslationErrorType.rateLimited:
        return ServiceStatus.degraded;
      case TranslationErrorType.authenticationError:
        return ServiceStatus.unavailable;
      case TranslationErrorType.invalidInput:
      case TranslationErrorType.unknownError:
        return ServiceStatus.unknown;
    }
  }

  /// Get the best available service for translation
  TranslateService? getBestAvailableService() {
    final availableServices = <TranslateService>[];
    final degradedServices = <TranslateService>[];

    for (final service in TranslateService.values) {
      final status = getServiceStatus(service);
      if (status == null) {
        // Unknown status, assume available
        availableServices.add(service);
      } else if (status.isAvailable) {
        availableServices.add(service);
      } else if (status.isDegraded) {
        degradedServices.add(service);
      }
    }

    // Prefer available services, then degraded ones
    if (availableServices.isNotEmpty) {
      return availableServices.first;
    } else if (degradedServices.isNotEmpty) {
      return degradedServices.first;
    }

    return null; // No services available
  }

  /// Get all service statuses
  Map<TranslateService, TranslationServiceStatus> getAllServiceStatuses() {
    return Map.from(_statusCache);
  }

  /// Clear status cache
  void clearStatusCache() {
    _statusCache.clear();
    AnxLog.info('Translation service status cache cleared');
  }
}
