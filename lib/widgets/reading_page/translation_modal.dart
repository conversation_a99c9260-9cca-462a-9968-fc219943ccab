import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/service/translate/index.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/page/book_player/epub_player.dart';

/// Translation modal component for the reading interface
/// Displays translated content with language selection and page navigation
class TranslationModal extends ConsumerStatefulWidget {
  const TranslationModal({
    super.key,
    required this.epubPlayerKey,
    required this.onClose,
  });

  final GlobalKey<EpubPlayerState> epubPlayerKey;
  final VoidCallback onClose;

  @override
  ConsumerState<TranslationModal> createState() => _TranslationModalState();
}

class _TranslationModalState extends ConsumerState<TranslationModal> {
  String _currentPageContent = '';
  String _translatedContent = '';
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  LangList _currentLanguage = Prefs().translateTo;
  bool _canNavigatePrev = true;
  bool _canNavigateNext = true;

  @override
  void initState() {
    super.initState();
    _updateNavigationState();
    _loadCurrentPageContent();
  }

  void _updateNavigationState() {
    final epubPlayerState = widget.epubPlayerKey.currentState;
    if (epubPlayerState != null) {
      setState(() {
        // For now, we'll assume navigation is always possible
        // In a more sophisticated implementation, we could check
        // the current position and book structure
        _canNavigatePrev = true;
        _canNavigateNext = true;
      });
    }
  }

  String _getErrorMessage(TranslationException e) {
    switch (e.type) {
      case TranslationErrorType.networkError:
        return 'Network error. Please check your internet connection and try again.';
      case TranslationErrorType.serviceUnavailable:
        return 'Translation service is temporarily unavailable. Please try again later.';
      case TranslationErrorType.rateLimited:
        return 'Too many requests. Please wait a moment and try again.';
      case TranslationErrorType.authenticationError:
        return 'Authentication failed. Please check your translation service settings.';
      case TranslationErrorType.invalidInput:
        return 'Invalid text for translation. Please select different content.';
      case TranslationErrorType.unknownError:
        return e.message.isNotEmpty
            ? e.message
            : L10n.of(context).translate_error;
    }
  }

  Future<void> _loadCurrentPageContent() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // Extract current page content from EPUB player
      final epubPlayerState = widget.epubPlayerKey.currentState;
      if (epubPlayerState == null) {
        throw Exception('EPUB player not available');
      }
      _currentPageContent = await epubPlayerState.getCurrentPageContent();

      if (_currentPageContent.isEmpty) {
        setState(() {
          _hasError = true;
          _errorMessage = 'No content available for translation';
          _isLoading = false;
        });
        return;
      }

      await _translateContent();
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _translateContent() async {
    if (_currentPageContent.isEmpty) return;

    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      final translatedText = await translateText(_currentPageContent);

      setState(() {
        _translatedContent = translatedText;
        _isLoading = false;
      });
    } on TranslationException catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = _getErrorMessage(e);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = L10n.of(context).translate_error;
        _isLoading = false;
      });
    }
  }

  Future<void> _changeLanguage(LangList newLanguage) async {
    if (newLanguage == _currentLanguage) return;

    setState(() {
      _currentLanguage = newLanguage;
    });

    // Update preferences
    Prefs().translateTo = newLanguage;

    // Re-translate with new language
    await _translateContent();
  }

  Future<void> _navigateToPage(bool isNext) async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      final epubPlayerState = widget.epubPlayerKey.currentState;
      if (epubPlayerState == null) {
        throw Exception('EPUB player not available');
      }

      // Navigate to next or previous page
      if (isNext) {
        epubPlayerState.nextPage();
      } else {
        epubPlayerState.prevPage();
      }

      // Wait a moment for the page to load
      await Future<void>.delayed(const Duration(milliseconds: 500));

      // Update navigation state
      _updateNavigationState();

      // Load and translate the new page content
      await _loadCurrentPageContent();
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final constraints =
        ResponsiveSystem.getOrientationAwareBottomSheetConstraints(context);

    return Container(
      constraints: constraints,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with language dropdown
          _buildHeader(),

          // Divider
          Divider(
            height: 1,
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),

          // Content area
          Expanded(
            child: _buildContent(),
          ),

          // Navigation controls
          _buildNavigationControls(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(DesignSystem.spaceM),
      child: Row(
        children: [
          Text(
            L10n.of(context).context_menu_translate,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const Spacer(),
          _buildLanguageDropdown(),
        ],
      ),
    );
  }

  Widget _buildLanguageDropdown() {
    return SemanticHelpers.button(
      context: context,
      label: 'Select translation language',
      hint: 'Current language: ${_currentLanguage.getNative(context)}',
      onTap: _showLanguagePicker,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: DesignSystem.spaceM,
          vertical: DesignSystem.spaceS,
        ),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
          ),
          borderRadius: BorderRadius.circular(DesignSystem.radiusM),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _currentLanguage.getNative(context),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(width: DesignSystem.spaceXS),
            Icon(
              Icons.keyboard_arrow_down,
              size: 20,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: DesignSystem.spaceM),
            Text(
              _errorMessage,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: DesignSystem.spaceM),
            TextButton(
              onPressed: _loadCurrentPageContent,
              child: Text(L10n.of(context).common_retry),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesignSystem.spaceM),
      child: Text(
        _translatedContent.isNotEmpty
            ? _translatedContent
            : 'No content to translate',
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              height: 1.6, // Better line height for reading
            ),
      ),
    );
  }

  Widget _buildNavigationControls() {
    return Container(
      padding: const EdgeInsets.all(DesignSystem.spaceM),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: (_isLoading || !_canNavigatePrev)
                  ? null
                  : () => _navigateToPage(false),
              icon: const Icon(Icons.arrow_back_ios, size: 16),
              label: const Text('Previous'),
            ),
          ),
          const SizedBox(width: DesignSystem.spaceM),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: (_isLoading || !_canNavigateNext)
                  ? null
                  : () => _navigateToPage(true),
              icon: const Icon(Icons.arrow_forward_ios, size: 16),
              label: const Text('Next'),
            ),
          ),
        ],
      ),
    );
  }

  void _showLanguagePicker() {
    showModalBottomSheet<void>(
      context: context,
      builder: (context) => TranslationLanguagePicker(
        currentLanguage: _currentLanguage,
        onLanguageSelected: _changeLanguage,
      ),
    );
  }
}

/// Language picker for translation modal
class TranslationLanguagePicker extends StatelessWidget {
  const TranslationLanguagePicker({
    super.key,
    required this.currentLanguage,
    required this.onLanguageSelected,
  });

  final LangList currentLanguage;
  final void Function(LangList) onLanguageSelected;

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(maxHeight: 400),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(DesignSystem.spaceM),
            child: Text(
              'Select Language',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: LangList.values.length,
              itemBuilder: (context, index) {
                final language = LangList.values[index];
                final isSelected = language == currentLanguage;

                return ListTile(
                  title: Text(language.getNative(context)),
                  subtitle: Text(
                    language.name[0].toUpperCase() + language.name.substring(1),
                  ),
                  trailing: isSelected ? const Icon(Icons.check) : null,
                  selected: isSelected,
                  onTap: () {
                    Navigator.pop(context);
                    onLanguageSelected(language);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
