import 'package:dasso_reader/config/color_system.dart';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/app_icons.dart';
import 'package:dasso_reader/dao/book_note.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book_note.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/service/translate/index.dart';
import 'package:dasso_reader/utils/haptic/haptic_feedback.dart';
import 'package:dasso_reader/utils/toast/common.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/widgets/dictionary/context_menu_dictionary_tab.dart';
import 'package:dasso_reader/widgets/dictionary/context_menu_word_segmentation_tab.dart';
import 'package:dasso_reader/widgets/dictionary/context_menu_character_tab.dart';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';

// Constants
const List<String> notesColors = [
  '66CCFF',
  'FF0000',
  '00FF00',
  'EB3BFF',
  'FFD700',
];
const List<Map<String, dynamic>> notesType = [
  {'type': 'highlight', 'icon': AntDesign.highlight_outline},
  {'type': 'underline', 'icon': Icons.format_underline},
];

// Performance optimization: Track consecutive menu invocations
DateTime _lastMenuShownTime = DateTime.fromMillisecondsSinceEpoch(0);
bool _isRapidInvocation = false;
const int _rapidInvocationThreshold = 500; // milliseconds

/// Shows the unified context menu when text is selected
void showUnifiedContextMenu(
  BuildContext context,
  double x,
  double y,
  String dir,
  String annoContent,
  String annoCfi,
  int? annoId,
  bool footnote,
) {
  // Check if this is a rapid invocation (for performance optimization)
  final now = DateTime.now();
  final timeSinceLastShow = now.difference(_lastMenuShownTime);
  _isRapidInvocation =
      timeSinceLastShow.inMilliseconds < _rapidInvocationThreshold;
  _lastMenuShownTime = now;

  // Create animation controller for the floating menu animation with optimized timing
  final animationController = AnimationController(
    duration: _isRapidInvocation
        ? Duration.zero // No animation during rapid invocation
        : DesignSystem
            .durationMedium, // Optimized for context menu responsiveness
    vsync: Navigator.of(context),
  );

  // Create scale and opacity animations with MD3 standard curves
  final scaleAnimation = CurvedAnimation(
    parent: animationController,
    // MD3 emphasized easing for scale
    curve: Curves.easeOutCubic,
  );

  final fadeAnimation = CurvedAnimation(
    parent: animationController,
    // MD3 standard easing for opacity
    curve: Curves.easeOutCubic,
  );

  final playerKey = epubPlayerKey.currentState!;

  // Remove any existing overlay first to prevent stacking
  playerKey.removeOverlay();

  double screenWidth = MediaQuery.of(context).size.width;
  double screenHeight = MediaQuery.of(context).size.height;

  // Calculate responsive menu width based on screen size
  double menuWidth;

  // Handle different device size classes using DesignSystem breakpoints
  if (DesignSystem.isSmallPhone(context)) {
    // Small phones
    menuWidth = screenWidth * 0.95; // Almost full width
  } else if (screenWidth < DesignSystem.breakpointTablet) {
    // Standard phones
    menuWidth = screenWidth * 0.85;
  } else if (screenWidth < DesignSystem.breakpointDesktop) {
    // Large phones, small tablets
    menuWidth = screenWidth * 0.65;
  } else {
    // Tablets and larger
    menuWidth = screenWidth * 0.45;
  }

  // Set min and max constraints based on device category
  if (screenWidth < DesignSystem.breakpointTablet) {
    menuWidth = menuWidth.clamp(280.0, 500.0);
  } else {
    menuWidth = menuWidth.clamp(400.0, 600.0); // Larger min/max for tablets
  }

  x *= screenWidth;
  y *= screenHeight;

  // Position the menu properly on screen
  double widgetLeft = x + menuWidth > screenWidth
      ? screenWidth - menuWidth - DesignSystem.spaceM
      : x;

  // Function to close the menu
  void onClose() {
    try {
      // Reverse the animation before removing the overlay
      if (!_isRapidInvocation &&
          animationController.status != AnimationStatus.dismissed) {
        animationController.reverse().then((_) {
          playerKey.webViewController
              .evaluateJavascript(source: 'clearSelection()');
          playerKey.removeOverlay();
        });
      } else {
        playerKey.webViewController
            .evaluateJavascript(source: 'clearSelection()');
        playerKey.removeOverlay();
      }
    } catch (e) {
      // Ensure overlay is removed even if JavaScript fails
      playerKey.removeOverlay();
    }
  }

  // Get current reading theme colors
  final readingBackgroundColor =
      Color(int.parse('0x${Prefs().readTheme.backgroundColor}'));
  final readingTextColor = Color(int.parse('0x${Prefs().readTheme.textColor}'));

  // Create the overlay entry with the unified menu
  playerKey.contextMenuEntry = OverlayEntry(
    builder: (context) {
      // Get the current keyboard visibility and safe area
      final bottomInset = MediaQuery.of(context).viewInsets.bottom;
      final isKeyboardVisible = bottomInset > 0;
      final safeArea = MediaQuery.of(context).padding;

      // Calculate estimated menu height (approximate for positioning decisions)
      // This is a rough estimate - actual height will depend on content
      final estimatedMenuHeight = annoContent.length > 50 ? 350.0 : 250.0;

      // Determine if menu should appear above or below selection
      // If selection is in the top half of screen, show menu below it
      // If selection is in the bottom half, show menu above it
      final bool showBelow = y < (screenHeight / 2);

      // Position variables - we'll use either top or bottom based on position
      double? top;
      double? bottom;

      if (showBelow) {
        // Show menu below selection
        top = y + DesignSystem.spaceXL; // Add some space below the selection

        // Check if menu would go off bottom of screen
        if (top + estimatedMenuHeight >
            screenHeight -
                safeArea.bottom -
                (isKeyboardVisible ? bottomInset : 0)) {
          // If it would go off bottom, flip to show above instead
          top = null;
          bottom = screenHeight - y + DesignSystem.spaceXL;
        }
      } else {
        // Show menu above selection
        bottom = screenHeight - y + DesignSystem.spaceXL;

        // Check if menu would go off top of screen
        if (bottom + estimatedMenuHeight > screenHeight - safeArea.top) {
          // If it would go off top, flip to show below instead
          bottom = null;
          top = y + DesignSystem.spaceXL;
        }
      }

      // Final safety check - if menu still doesn't fit, center it on screen
      if ((top != null &&
              top + estimatedMenuHeight >
                  screenHeight -
                      safeArea.bottom -
                      (isKeyboardVisible ? bottomInset : 0)) ||
          (bottom != null &&
              bottom + estimatedMenuHeight > screenHeight - safeArea.top)) {
        // Center the menu on screen as a fallback
        top = (screenHeight - estimatedMenuHeight) / 2;
        bottom = null;
      }

      // Ensure menu is visible if keyboard is shown
      if (isKeyboardVisible &&
          top != null &&
          top + estimatedMenuHeight > screenHeight - bottomInset) {
        // Adjust to be above keyboard
        top = screenHeight -
            bottomInset -
            estimatedMenuHeight -
            DesignSystem.spaceM;
      }

      return StatefulBuilder(
        builder: (context, setState) {
          final ValueNotifier<bool> showColorPalette =
              ValueNotifier<bool>(false);

          // State for selected tab - 0: Dict, 1: Set/Word, 2: CHAR
          final ValueNotifier<int> selectedTabIndex = ValueNotifier<int>(0);

          return Stack(
            children: [
              // Invisible full-screen button to detect taps outside the menu
              Positioned.fill(
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: onClose,
                  child: Container(
                    color: Colors.transparent,
                  ),
                ),
              ),
              // Context-Aware Floating Menu with scale and fade animation
              Positioned(
                left: widgetLeft,
                top: top,
                bottom: bottom,
                child: AnimatedBuilder(
                  animation: animationController,
                  builder: (context, child) {
                    return Transform.scale(
                      // MD3 standard scale values (0.95 to 1.0)
                      scale: _isRapidInvocation
                          ? 1.0
                          : 0.95 + (0.05 * scaleAnimation.value),
                      child: Opacity(
                        opacity: _isRapidInvocation ? 1.0 : fadeAnimation.value,
                        child: child,
                      ),
                    );
                  },
                  child: PointerInterceptor(
                    child: Container(
                      width: menuWidth,
                      constraints: BoxConstraints(
                        maxHeight: isKeyboardVisible
                            ? MediaQuery.of(context).size.height -
                                bottomInset -
                                DesignSystem.spaceXL
                            : MediaQuery.of(context).size.height * 0.7,
                      ),
                      decoration: BoxDecoration(
                        color: readingBackgroundColor,
                        borderRadius: BorderRadius.circular(
                          DesignSystem.radiusL,
                        ), // 16.0 (preserves exact radius)
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(80),
                            blurRadius: DesignSystem.getAdjustedElevation(
                              DesignSystem.spaceMicro,
                            ),
                            spreadRadius: 0,
                            offset: const Offset(0, 0),
                          ),
                          BoxShadow(
                            color: Colors.black.withAlpha(50),
                            blurRadius: DesignSystem.getAdjustedElevation(
                              DesignSystem.spaceXS,
                            ),
                            spreadRadius: -DesignSystem.getAdjustedElevation(
                              DesignSystem.spaceTiny,
                            ),
                            offset: const Offset(0, 0),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            DesignSystem.radiusL,
                          ), // 16.0 (preserves exact radius)
                        ),
                        clipBehavior: Clip.antiAlias,
                        child: DefaultTextStyle(
                          style: TextStyle(color: readingTextColor),
                          child: Theme(
                            data: Theme.of(context).copyWith(
                              iconTheme: IconThemeData(color: readingTextColor),
                              dividerColor:
                                  readingTextColor.withValues(alpha: 0.2),
                              colorScheme: ColorScheme.fromSeed(
                                seedColor:
                                    Theme.of(context).colorScheme.primary,
                                brightness:
                                    ThemeData.estimateBrightnessForColor(
                                  readingBackgroundColor,
                                ),
                                primary: Theme.of(context).colorScheme.primary,
                                onPrimary:
                                    Theme.of(context).colorScheme.onPrimary,
                                secondary:
                                    Theme.of(context).colorScheme.secondary,
                                surface: readingBackgroundColor,
                                onSurface: readingTextColor,
                                surfaceContainerLow: readingBackgroundColor
                                    .withValues(alpha: 0.9),
                                outlineVariant:
                                    readingTextColor.withValues(alpha: 0.2),
                              ),
                            ),
                            child: SemanticHelpers.container(
                              label: 'Text selection context menu',
                              contentType: 'reading',
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Combined Action buttons and tabs tray with state management
                                  _buildCombinedActionAndTabsRow(
                                    context,
                                    annoId,
                                    footnote,
                                    annoContent,
                                    annoCfi,
                                    onClose,
                                    showColorPalette,
                                    readingTextColor,
                                    annoContent,
                                    epubPlayerKey.currentState!.widget.book.id,
                                    selectedTabIndex, // Pass the tab state
                                  ),

                                  // If annotation exists and has notes, show the optimized note section
                                  if (annoId != null)
                                    FutureBuilder<BookNote?>(
                                      future: selectBookNoteById(annoId),
                                      builder: (context, snapshot) {
                                        if (snapshot.connectionState ==
                                                ConnectionState.done &&
                                            snapshot.hasData &&
                                            snapshot.data?.readerNote != null &&
                                            snapshot
                                                .data!.readerNote!.isNotEmpty) {
                                          final noteText =
                                              snapshot.data!.readerNote!;

                                          return Column(
                                            children: [
                                              // Thinner divider
                                              // MD3 divider with proper color token
                                              Divider(
                                                color: readingTextColor
                                                    .withValues(alpha: 0.2),
                                                height: DesignSystem.spaceMicro,
                                                thickness:
                                                    DesignSystem.spaceMicro,
                                              ),
                                              // Simple container with subtle background (non-interactive)
                                              Container(
                                                margin:
                                                    const EdgeInsets.symmetric(
                                                  horizontal:
                                                      DesignSystem.spaceS,
                                                  vertical:
                                                      DesignSystem.spaceXS,
                                                ),
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                  horizontal:
                                                      DesignSystem.spaceS,
                                                  vertical:
                                                      DesignSystem.spaceXS,
                                                ),
                                                decoration: BoxDecoration(
                                                  // MD3 color token for subtle container backgrounds
                                                  color: readingBackgroundColor
                                                      .withValues(alpha: 0.8),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                    DesignSystem.radiusS,
                                                  ),
                                                ),
                                                child: Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    // Smaller icon with less spacing
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                        top: DesignSystem
                                                            .spaceTiny,
                                                      ), // Align with text
                                                      child: Icon(
                                                        Icons.note_alt,
                                                        size: DesignSystem
                                                            .getAdjustedIconSize(
                                                          AppIcons.sizeS,
                                                        ), // Smaller icon with manufacturer adjustment
                                                        // MD3 color token for secondary icons
                                                        color: readingTextColor,
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      width:
                                                          DesignSystem.spaceXS,
                                                    ), // Reduced spacing
                                                    // Note text with optimized typography
                                                    Expanded(
                                                      child: Directionality(
                                                        textDirection: Localizations
                                                                        .localeOf(
                                                                      context,
                                                                    ).languageCode ==
                                                                    'ar' ||
                                                                Localizations.localeOf(
                                                                      context,
                                                                    ).languageCode ==
                                                                    'he'
                                                            ? TextDirection.rtl
                                                            : TextDirection.ltr,
                                                        child: Text(
                                                          noteText,
                                                          // MD3 typography - bodySmall
                                                          style:
                                                              Theme.of(context)
                                                                  .textTheme
                                                                  .bodySmall
                                                                  ?.copyWith(
                                                                    height:
                                                                        1.2, // Maintain tighter line height
                                                                    color:
                                                                        readingTextColor,
                                                                  ),
                                                          maxLines:
                                                              2, // Show max 2 lines by default
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          );
                                        }
                                        return const SizedBox.shrink();
                                      },
                                    ),

                                  // Removed duplicate selected text content to save vertical space

                                  // Tabbed content (Dictionary and Word Segmentation) - without tab bar
                                  ValueListenableBuilder<int>(
                                    valueListenable: selectedTabIndex,
                                    builder: (context, tabIndex, _) {
                                      return _buildTabbedContentWithoutTabs(
                                        context,
                                        annoContent,
                                        epubPlayerKey
                                            .currentState!.widget.book.id,
                                        tabIndex,
                                      );
                                    },
                                  ),

                                  // Translation widget (always visible)
                                  _buildInlineTranslationWidget(
                                    context,
                                    annoContent,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      );
    },
  );

  // Add haptic feedback when menu appears
  AnxHapticFeedback.lightImpact();

  // Add the overlay to the player
  Overlay.of(context).insert(playerKey.contextMenuEntry!);

  // Start the animation
  if (!_isRapidInvocation) {
    animationController.forward();
  }
}

/// Creates an annotation with the selected type and color
Future<void> onAnnotationSelected(
  String type,
  String color,
  String content,
  String cfi,
  void Function() onClose,
) async {
  Prefs().annotationType = type;
  Prefs().annotationColor = color;

  BookNote bookNote = BookNote(
    id: null,
    bookId: epubPlayerKey.currentState!.widget.book.id,
    content: content,
    cfi: cfi,
    chapter: epubPlayerKey.currentState!.chapterTitle,
    type: type,
    color: color,
    createTime: DateTime.now(),
    updateTime: DateTime.now(),
  );

  int noteId = await insertBookNote(bookNote);
  bookNote.setId(noteId);
  epubPlayerKey.currentState!.addAnnotation(bookNote);
  onClose();
}

/// Shows a modal dialog to edit reader notes
Future<void> showNoteEditor(
  BuildContext context,
  int? noteId,
  String content,
  String cfi,
  void Function() onClose,
) async {
  // Capture localized strings and navigator context at the very beginning
  final addNoteTips = L10n.of(context).context_menu_add_note_tips;
  final cancelText = L10n.of(context).common_cancel;
  final saveText = L10n.of(context).common_save;
  final navigatorContext = Navigator.of(context).context;

  final textController = TextEditingController();
  BookNote? existingNote;

  // If there's an existing note, load it
  if (noteId != null) {
    existingNote = await selectBookNoteById(noteId);
    if (existingNote.readerNote != null) {
      textController.text = existingNote.readerNote!;
    }
  }
  // Otherwise create a new annotation first
  else {
    // Create a new annotation first
    BookNote bookNote = BookNote(
      id: null,
      bookId: epubPlayerKey.currentState!.widget.book.id,
      content: content,
      cfi: cfi,
      chapter: epubPlayerKey.currentState!.chapterTitle,
      type: Prefs().annotationType,
      color: Prefs().annotationColor,
      readerNote: '',
      createTime: DateTime.now(),
      updateTime: DateTime.now(),
    );

    noteId = await insertBookNote(bookNote);
    bookNote.setId(noteId);
    epubPlayerKey.currentState!.addAnnotation(bookNote);
  }

  // Close the context menu
  onClose();

  // Check if context is still valid before showing dialog
  if (!context.mounted) return;

  // Use a more reliable method to show the dialog after the context menu is closed
  WidgetsBinding.instance.addPostFrameCallback((_) {
    if (!navigatorContext.mounted) return;

    showDialog<void>(
      context: navigatorContext,
      barrierDismissible: true,
      builder: (dialogContext) => AlertDialog(
        title: Text(
          addNoteTips, // Use captured string
          // MD3 typography - titleLarge for dialog titles
          style: Theme.of(dialogContext).textTheme.titleLarge,
        ),
        content: TextField(
          controller: textController,
          autofocus: true,
          maxLines: 5,
          minLines: 3,
          decoration: InputDecoration(
            hintText: addNoteTips, // Use captured string
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(DesignSystem.radiusM),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
            },
            child: Text(
              cancelText, // Use captured string
              // MD3 typography - labelLarge for button text
              style: Theme.of(dialogContext).textTheme.labelLarge,
            ),
          ),
          TextButton(
            onPressed: () async {
              if (noteId != null) {
                // Capture context for async operations
                final currentContext = dialogContext;
                final savedMessage = L10n.of(currentContext).common_saved;

                BookNote note = await selectBookNoteById(noteId);
                note.readerNote = textController.text.trim();
                await updateBookNoteById(note);

                // Check if context is still valid
                if (currentContext.mounted) {
                  AnxToast.show(savedMessage);
                  Navigator.of(currentContext).pop();
                }
              }
            },
            child: Text(
              saveText, // Use captured string
              // MD3 typography - labelLarge for button text
              style: Theme.of(dialogContext).textTheme.labelLarge,
            ),
          ),
        ],
      ),
    );
  });
}

/// Builds the color selector row that slides in when color button is tapped
Widget _buildColorSelector(
  BuildContext context,
  VoidCallback onBack,
  String content,
  String cfi,
  void Function() onClose,
) {
  return Row(
    key: const ValueKey('colorSelector'),
    children: [
      // Back button
      CircleIconButton(
        icon: Icons.arrow_back,
        onTap: onBack,
      ),
      const SizedBox(width: DesignSystem.spaceS),
      // Color options
      for (String color in notesColors)
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spaceXS),
          child: InkWell(
            borderRadius: BorderRadius.circular(DesignSystem.radiusM),
            onTap: () {
              Prefs().annotationColor = color;
              // Create annotation with selected color
              onAnnotationSelected(
                Prefs().annotationType,
                color,
                content,
                cfi,
                onClose,
              );
            },
            child: Container(
              width: DesignSystem.spaceXL - DesignSystem.spaceTiny,
              height: DesignSystem.spaceXL - DesignSystem.spaceTiny,
              decoration: BoxDecoration(
                color: Color(int.parse('0xFF$color')),
                shape: BoxShape.circle,
                // Use simple border for selected state
                border: Border.all(
                  color: Prefs().annotationColor == color
                      ? Theme.of(context).colorScheme.primary
                      : Colors.transparent,
                  width: DesignSystem.spaceTiny,
                ),
              ),
            ),
          ),
        ),
    ],
  );
}

/// Builds the combined action buttons and tabs row to save vertical space
Widget _buildCombinedActionAndTabsRow(
  BuildContext context,
  int? annoId,
  bool footnote,
  String annoContent,
  String annoCfi,
  void Function() onClose,
  ValueNotifier<bool> showColorPalette,
  Color readingTextColor,
  String selectedText,
  int bookId,
  ValueNotifier<int> selectedTabIndex,
) {
  final screenWidth = MediaQuery.of(context).size.width;
  final isChinese = RegExp(r'[\u4e00-\u9fa5]').hasMatch(selectedText);

  // Determine if we should show tabs
  final shouldShowTabs = isChinese;

  return Container(
    decoration: BoxDecoration(
      border: Border(
        bottom: BorderSide(
          color: readingTextColor.withValues(alpha: 0.2),
          width: DesignSystem.spaceMicro,
        ),
      ),
    ),
    padding: DesignSystem.getAdaptivePaddingWithManufacturerAdjustment(
      context,
      const EdgeInsets.symmetric(
        horizontal: DesignSystem.spaceS, // 8.0
        vertical: DesignSystem.spaceS, // 8.0
      ),
    ),
    child: ValueListenableBuilder<bool>(
      valueListenable: showColorPalette,
      builder: (context, isShowingColors, _) {
        // When showing color palette, show it full width
        if (isShowingColors) {
          return _buildColorSelector(
            context,
            () => showColorPalette.value = false,
            annoContent,
            annoCfi,
            onClose,
          );
        }

        // Normal state: show action buttons and tabs in the same row
        return Row(
          children: [
            // Action buttons section (left side) - use Flexible to prevent overflow
            Flexible(
              flex: shouldShowTabs ? 3 : 1,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                physics: const ClampingScrollPhysics(),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: _buildCompactActionButtons(
                    context,
                    annoId,
                    footnote,
                    annoContent,
                    annoCfi,
                    onClose,
                    showColorPalette,
                    screenWidth,
                  ),
                ),
              ),
            ),

            // Tabs section (right side) - only for Chinese text
            if (shouldShowTabs) ...[
              // Use manufacturer-adjusted spacing for pixel-perfect consistency
              SizedBox(
                width:
                    DesignSystem.getAdaptiveSpacingWithManufacturerAdjustment(
                  context,
                  DesignSystem.getContentDensitySpacing(ContentDensity.compact),
                ),
              ),
              Flexible(
                flex: 2,
                child: _buildCompactTabBar(
                  context,
                  selectedText,
                  bookId,
                  readingTextColor,
                  selectedTabIndex,
                ),
              ),
            ],
          ],
        );
      },
    ),
  );
}

/// Builds compact action buttons optimized for shared row space
List<Widget> _buildCompactActionButtons(
  BuildContext context,
  int? annoId,
  bool footnote,
  String annoContent,
  String annoCfi,
  void Function() onClose,
  ValueNotifier<bool> showColorPalette,
  double screenWidth,
) {
  // Use smaller buttons for compact layout
  final isCompact = screenWidth < 600;

  // Build list of buttons with proper spacing
  final buttons = <Widget>[];

  // Delete button (only for existing annotations)
  if (annoId != null) {
    buttons.add(
      _CompactCircleIconButton(
        icon: Icons.delete_outline,
        semanticsLabel: L10n.of(context).common_delete,
        isCompact: isCompact,
        onTap: () {
          AnxHapticFeedback.mediumImpact();
          deleteBookNoteById(annoId);
          epubPlayerKey.currentState!.removeAnnotation(annoCfi);
          AnxToast.show(L10n.of(context).common_delete);
          onClose();
        },
      ),
    );
  }

  // Highlight button (only for new annotations)
  if (annoId == null) {
    buttons.add(
      _CompactCircleIconButton(
        icon: Icons.brush,
        semanticsLabel: L10n.of(context).context_menu_highlight,
        isCompact: isCompact,
        onTap: !footnote
            ? () {
                AnxHapticFeedback.selectionClick();
                onAnnotationSelected(
                  'highlight',
                  Prefs().annotationColor,
                  annoContent,
                  annoCfi,
                  onClose,
                );
              }
            : null,
      ),
    );
  }

  // Color button (only for new annotations)
  if (annoId == null) {
    buttons.add(
      _CompactCircleIconButton(
        icon: Icons.color_lens,
        semanticsLabel: 'Choose highlight color',
        showColorIndicator: true,
        colorValue: Prefs().annotationColor,
        isCompact: isCompact,
        onTap: !footnote
            ? () {
                AnxHapticFeedback.lightImpact();
                showColorPalette.value = true;
              }
            : null,
      ),
    );
  }

  // Note button
  buttons.add(
    _CompactCircleIconButton(
      icon: Icons.note_alt_outlined,
      semanticsLabel: L10n.of(context).context_menu_add_note_tips,
      isCompact: isCompact,
      onTap: !footnote
          ? () {
              AnxHapticFeedback.lightImpact();
              showNoteEditor(
                context,
                annoId,
                annoContent,
                annoCfi,
                onClose,
              );
            }
          : null,
    ),
  );

  // AI chat button
  buttons.add(
    _CompactCircleIconButton(
      icon: EvaIcons.message_circle_outline,
      semanticsLabel: L10n.of(context).ai_chat,
      isCompact: isCompact,
      onTap: () {
        AnxHapticFeedback.mediumImpact();
        onClose();
        final key = readingPageKey.currentState;
        if (key != null) {
          key.showAiChat(
            content: annoContent,
            sendImmediate: false,
          );
          key.aiChatKey.currentState?.inputController.text = annoContent;
        }
      },
    ),
  );

  // Add manufacturer-adjusted spacing between buttons for pixel-perfect consistency
  final spacedButtons = <Widget>[];
  for (int i = 0; i < buttons.length; i++) {
    spacedButtons.add(buttons[i]);
    // Add spacing between buttons (but not after the last one)
    if (i < buttons.length - 1) {
      spacedButtons.add(
        SizedBox(
          width: DesignSystem.getAdaptiveSpacingWithManufacturerAdjustment(
            context,
            DesignSystem.getContentDensitySpacing(ContentDensity.compact),
          ),
        ),
      );
    }
  }

  return spacedButtons;
}

/// Builds a compact tab bar for the combined row layout
Widget _buildCompactTabBar(
  BuildContext context,
  String selectedText,
  int bookId,
  Color readingTextColor,
  ValueNotifier<int> selectedTabIndex,
) {
  // Create a simplified tab controller for the compact layout
  return _CompactTabBarWidget(
    selectedText: selectedText,
    bookId: bookId,
    readingTextColor: readingTextColor,
    selectedTabIndex: selectedTabIndex,
  );
}

/// Builds tabbed content without the tab bar (since tabs are now in the top row)
Widget _buildTabbedContentWithoutTabs(
  BuildContext context,
  String selectedText,
  int bookId,
  int selectedTabIndex,
) {
  final readingTextColor = Color(int.parse('0x${Prefs().readTheme.textColor}'));
  final isChinese = RegExp(r'[\u4e00-\u9fa5]').hasMatch(selectedText);

  return Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      // MD3 divider
      Divider(
        color: readingTextColor.withValues(alpha: 0.2),
        height: DesignSystem.spaceMicro,
        thickness: DesignSystem.spaceMicro,
      ),

      // Content area - dynamically show content based on selected tab
      SizedBox(
        height: isChinese ? 200 : 150,
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: _buildTabContent(selectedText, bookId, selectedTabIndex),
        ),
      ),
    ],
  );
}

/// Builds the appropriate tab content based on the selected tab index
Widget _buildTabContent(String selectedText, int bookId, int tabIndex) {
  final isChinese = RegExp(r'[\u4e00-\u9fa5]').hasMatch(selectedText);

  if (!isChinese) {
    // For non-Chinese text, always show dictionary
    return ContextMenuDictionaryTab(text: selectedText);
  }

  // For Chinese text, determine available tabs and show appropriate content
  final availableTabs = _getAvailableTabs(selectedText);

  // Ensure tabIndex is within bounds
  final safeTabIndex = tabIndex.clamp(0, availableTabs.length - 1);
  final selectedTab = availableTabs[safeTabIndex];

  switch (selectedTab) {
    case 'Dict':
      return ContextMenuDictionaryTab(text: selectedText);
    case 'Set':
      // Import the word segmentation tab when needed
      return _buildWordSegmentationContent(selectedText, bookId);
    case 'Char':
      // Import the character tab when needed
      return _buildCharacterContent(selectedText);
    default:
      return ContextMenuDictionaryTab(text: selectedText);
  }
}

/// Get available tabs for the given text
List<String> _getAvailableTabs(String selectedText) {
  final tabs = <String>[];
  final isChinese = RegExp(r'[\u4e00-\u9fa5]').hasMatch(selectedText);

  if (isChinese) {
    tabs.add('Dict');

    // Add Set/Word tab if meaningful segmentation is likely
    if (selectedText.length > 2) {
      tabs.add('Set');
    }

    // Add Char tab for multiple characters or polyphonic single characters
    final chineseChars = selectedText
        .split('')
        .where((c) => RegExp(r'[\u4e00-\u9fa5]').hasMatch(c))
        .toList();
    if (chineseChars.length > 1 || _isPolyphonic(chineseChars.first)) {
      tabs.add('Char'); // Changed from 'CHAR' to 'Char'
    }
  }

  return tabs;
}

/// Check if a character is polyphonic
bool _isPolyphonic(String character) {
  const commonPolyphonicChars = {
    '没',
    '得',
    '的',
    '地',
    '着',
    '了',
    '中',
    '长',
    '大',
    '小',
    '好',
    '行',
    '发',
    '会',
    '要',
    '还',
    '都',
    '为',
    '和',
    '种',
    '重',
    '应',
    '便',
    '间',
    '传',
    '教',
    '数',
    '处',
    '调',
    '背',
    '当',
    '正',
    '几',
    '分',
    '切',
    '单',
    '干',
    '华',
    '结',
    '解',
    '空',
    '累',
    '量',
    '论',
    '难',
    '强',
    '任',
    '散',
    '少',
    '省',
    '识',
    '似',
    '提',
    '系',
    '相',
    '兴',
    '血',
    '压',
    '一',
    '音',
    '与',
    '语',
    '载',
    '占',
    '只',
    '转',
    '作',
    '做',
    '钻',
  };
  return commonPolyphonicChars.contains(character);
}

/// Word segmentation content using the actual widget
Widget _buildWordSegmentationContent(String selectedText, int bookId) {
  return ContextMenuWordSegmentationTab(
    selectedText: selectedText,
    bookId: bookId,
    startOffset: 0,
    endOffset: selectedText.length,
  );
}

/// Character content using the actual widget
Widget _buildCharacterContent(String selectedText) {
  return ContextMenuCharacterTab(
    selectedText: selectedText,
  );
}

/// Builds a simplified translation widget that displays under the selected text
/// with improved loading state that shows language dropdown immediately
Widget _buildInlineTranslationWidget(BuildContext context, String text) {
  // Use a stateful widget to properly manage the translation state
  return _TranslationWidget(text: text);
}

/// A stateful widget for the translation section to properly manage state
class _TranslationWidget extends StatefulWidget {
  final String text;

  const _TranslationWidget({required this.text});

  @override
  State<_TranslationWidget> createState() => _TranslationWidgetState();
}

class _TranslationWidgetState extends State<_TranslationWidget> {
  String? translatedText;
  bool isLoading = true;
  bool hasError = false;
  late LangList currentLanguage;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    // Store current language to detect changes
    currentLanguage = Prefs().translateTo;
    _loadTranslationDebounced();

    // Listen for language changes
    Prefs().addListener(_onPrefsChanged);
  }

  @override
  void dispose() {
    // Cancel any pending debounced translation
    _debounceTimer?.cancel();
    // Remove listener when widget is disposed
    Prefs().removeListener(_onPrefsChanged);
    super.dispose();
  }

  void _onPrefsChanged() {
    // Check if the translation language has changed
    final newLanguage = Prefs().translateTo;
    if (newLanguage != currentLanguage) {
      currentLanguage = newLanguage;
      // Show loading state and reload translation with debouncing
      setState(() {
        isLoading = true;
        hasError = false;
      });
      _loadTranslationDebounced();
    }
  }

  /// Debounced translation loading to prevent excessive API calls
  void _loadTranslationDebounced() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      _loadTranslation();
    });
  }

  Future<void> _loadTranslation() async {
    if (!mounted) return;

    try {
      final result = await translateText(widget.text);
      if (mounted) {
        setState(() {
          translatedText = result;
          isLoading = false;
          hasError = false;
        });
      }
    } on TranslationException catch (e) {
      if (mounted) {
        setState(() {
          translatedText = _getContextMenuErrorMessage(e);
          isLoading = false;
          hasError = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          translatedText = L10n.of(context).translate_error;
          isLoading = false;
          hasError = true;
        });
      }
    }
  }

  String _getContextMenuErrorMessage(TranslationException e) {
    switch (e.type) {
      case TranslationErrorType.networkError:
        return L10n.of(context).translation_context_error_network;
      case TranslationErrorType.serviceUnavailable:
        return L10n.of(context).translation_context_error_service_unavailable;
      case TranslationErrorType.rateLimited:
        return L10n.of(context).translation_context_error_rate_limited;
      case TranslationErrorType.authenticationError:
        return L10n.of(context).translation_context_error_authentication;
      case TranslationErrorType.invalidInput:
        return L10n.of(context).translation_context_error_invalid_input;
      case TranslationErrorType.unknownError:
        return L10n.of(context).translation_context_error_unknown;
    }
  }

  void _copyToClipboard() {
    if (translatedText != null) {
      // Copy text to clipboard - this is instantaneous
      Clipboard.setData(ClipboardData(text: translatedText!));
      AnxToast.show(L10n.of(context).notes_page_copied);
      HapticFeedback.lightImpact();
    }
  }

  /// Build skeleton loader for better loading UX
  Widget _buildSkeletonLoader(Color readingTextColor) {
    return SizedBox(
      height: DesignSystem.spaceL, // Fixed height to prevent layout shifts
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // First line skeleton
          Container(
            height: 12,
            width: double.infinity,
            decoration: BoxDecoration(
              color: readingTextColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(DesignSystem.radiusS),
            ),
          ),
          const SizedBox(height: DesignSystem.spaceTiny),
          // Second line skeleton (shorter)
          Container(
            height: 12,
            width: MediaQuery.of(context).size.width * 0.6,
            decoration: BoxDecoration(
              color: readingTextColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(DesignSystem.radiusS),
            ),
          ),
        ],
      ),
    );
  }

  /// Build translated text with proper styling and error handling
  Widget _buildTranslatedText(Color readingTextColor) {
    return Directionality(
      textDirection: Localizations.localeOf(context).languageCode == 'ar' ||
              Localizations.localeOf(context).languageCode == 'he'
          ? TextDirection.rtl
          : TextDirection.ltr,
      child: Text(
        translatedText!,
        // MD3 typography - bodyMedium
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              // Responsive font size based on screen width
              fontSize: DesignSystem.isSmallPhone(context)
                  ? DesignSystem.fontSizeS
                  : null,
              color: hasError
                  ? Theme.of(context).colorScheme.error
                  : readingTextColor,
              fontStyle: hasError ? FontStyle.italic : null,
            ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get reading theme colors
    final readingTextColor =
        Color(int.parse('0x${Prefs().readTheme.textColor}'));

    return Padding(
      padding: const EdgeInsets.fromLTRB(
        DesignSystem.spaceM,
        0.0,
        DesignSystem.spaceM,
        DesignSystem.spaceXS,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Divider is always visible
          // MD3 divider with proper color token
          Divider(
            color: readingTextColor.withValues(alpha: 0.2),
            height: DesignSystem.spaceMicro,
            thickness: DesignSystem.spaceMicro,
          ),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: DesignSystem.spaceS + DesignSystem.spaceTiny,
              vertical: DesignSystem.spaceXS + DesignSystem.spaceTiny,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Translated text area with optimized loading state
                Expanded(
                  child: isLoading
                      ? _buildSkeletonLoader(readingTextColor)
                      : translatedText == null
                          ? const SizedBox.shrink()
                          : _buildTranslatedText(readingTextColor),
                ),
                // Controls on the right - always visible
                const SizedBox(width: DesignSystem.spaceXS),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Copy button - only visible when translation is complete
                    isLoading || translatedText == null
                        ? SizedBox(
                            width: DesignSystem.getAdjustedIconSize(
                              AppIcons.sizeS,
                            ),
                          ) // Placeholder to maintain layout
                        : IconButton(
                            icon: Icon(
                              Icons.copy_outlined,
                              size: DesignSystem.getAdjustedIconSize(
                                AppIcons.sizeS,
                              ),
                              color: readingTextColor,
                            ),
                            onPressed: _copyToClipboard,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                    const SizedBox(width: DesignSystem.spaceTiny),
                    // Language dropdown - always visible
                    _buildCompactLanguageDropdown(context),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Builds a compact language dropdown showing only the destination language with flag
Widget _buildCompactLanguageDropdown(BuildContext context) {
  final MenuController menuController = MenuController();
  final currentLang = Prefs().translateTo;

  // Get reading theme colors
  final readingTextColor = Color(int.parse('0x${Prefs().readTheme.textColor}'));
  final readingBackgroundColor =
      Color(int.parse('0x${Prefs().readTheme.backgroundColor}'));

  // Map of language codes to flag emojis (simplified for example)
  final Map<String, String> flagEmojis = {
    'en': '🇬🇧',
    'zh-CN': '🇨🇳',
    'zh-TW': '🇹🇼',
    'es': '🇪🇸',
    'fr': '🇫🇷',
    'de': '🇩🇪',
    'ja': '🇯🇵',
    'ko': '🇰🇷',
    'ru': '🇷🇺',
    'ar': '🇸🇦',
    'bg': '🇧🇬',
    'ca': '🇪🇸',
    'hr': '🇭🇷',
    'cs': '🇨🇿',
    'da': '🇩🇰',
    'nl': '🇳🇱',
    'fi': '🇫🇮',
    'el': '🇬🇷',
    'hi': '🇮🇳',
    'hu': '🇭🇺',
    'id': '🇮🇩',
    'it': '🇮🇹',
    'ms': '🇲🇾',
    'mt': '🇲🇹',
    'nb': '🇳🇴',
    'pl': '🇵🇱',
    'pt': '🇵🇹',
    'ro': '🇷🇴',
    'sk': '🇸🇰',
    'sl': '🇸🇮',
    'sv': '🇸🇪',
    'ta': '🇮🇳',
    'te': '🇮🇳',
    'th': '🇹🇭',
    'tr': '🇹🇷',
    'uk': '🇺🇦',
    'vi': '🇻🇳',
  };

  // Get flag for current language or default to empty string
  String getFlag(String code) => flagEmojis[code] ?? '';

  return MenuAnchor(
    style: MenuStyle(
      backgroundColor: WidgetStateProperty.all(
        readingBackgroundColor,
      ),
      maximumSize: WidgetStateProperty.all(const Size(200, 140)),
    ),
    controller: menuController,
    alignmentOffset: const Offset(
      -DesignSystem.spaceXL - DesignSystem.spaceL,
      0,
    ), // Shift the menu to the left
    menuChildren: [
      // Create a ListView with ConstrainedBox to make it scrollable but contained
      ConstrainedBox(
        constraints: const BoxConstraints(maxHeight: 140),
        child: DefaultTextStyle(
          style: TextStyle(color: readingTextColor),
          child: SingleChildScrollView(
            // Use a dedicated ScrollController to avoid conflicts with PrimaryScrollController
            controller: ScrollController(),
            // Disable automatic use of PrimaryScrollController
            primary: false,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: LangList.values.map((lang) {
                // Skip Auto language option
                if (lang == LangList.auto) return const SizedBox.shrink();

                return MenuItemButton(
                  onPressed: () {
                    Prefs().translateTo = lang;
                    menuController.close();
                  },
                  style: MenuItemButton.styleFrom(
                    minimumSize: const Size.fromHeight(
                      DesignSystem.widgetMinTouchTarget,
                    ),
                    foregroundColor: readingTextColor,
                  ),
                  child: Row(
                    children: [
                      Text(
                        getFlag(lang.code),
                        style: TextStyle(
                          fontSize: DesignSystem.fontSizeM,
                          fontWeight: DesignSystem.getAdjustedFontWeight(
                            FontWeight.normal,
                          ),
                        ),
                      ),
                      const SizedBox(width: DesignSystem.spaceS),
                      Expanded(
                        child: Text(
                          lang.getNative(context),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ),
    ],
    builder: (context, controller, child) {
      return InkWell(
        onTap: () {
          if (controller.isOpen) {
            controller.close();
          } else {
            controller.open();
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: DesignSystem.spaceXS + DesignSystem.spaceTiny,
            vertical: DesignSystem.spaceXS - DesignSystem.spaceMicro,
          ),
          decoration: BoxDecoration(
            border: Border.all(color: readingTextColor.withValues(alpha: 0.2)),
            borderRadius: BorderRadius.circular(DesignSystem.radiusL),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                getFlag(currentLang.code),
                style: TextStyle(
                  fontSize: DesignSystem.fontSizeM,
                  fontWeight:
                      DesignSystem.getAdjustedFontWeight(FontWeight.normal),
                ),
              ),
              const SizedBox(width: DesignSystem.spaceXS / 2),
              // Only show language code instead of full name
              Text(
                currentLang.code.split('-')[0].toUpperCase(),
                style: TextStyle(color: readingTextColor),
              ),
              Icon(
                Icons.arrow_drop_down,
                size: DesignSystem.getAdjustedIconSize(AppIcons.sizeXS),
                color: readingTextColor,
              ),
            ],
          ),
        ),
      );
    },
  );
}

/// A compact tab bar widget for the combined row layout
class _CompactTabBarWidget extends StatefulWidget {
  final String selectedText;
  final int bookId;
  final Color readingTextColor;
  final ValueNotifier<int> selectedTabIndex;

  const _CompactTabBarWidget({
    required this.selectedText,
    required this.bookId,
    required this.readingTextColor,
    required this.selectedTabIndex,
  });

  @override
  State<_CompactTabBarWidget> createState() => _CompactTabBarWidgetState();
}

class _CompactTabBarWidgetState extends State<_CompactTabBarWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _tabLabels = [];

  @override
  void initState() {
    super.initState();
    _initializeTabs();
    _tabController = TabController(
      length: _tabLabels.length,
      vsync: this,
      initialIndex:
          widget.selectedTabIndex.value.clamp(0, _tabLabels.length - 1),
    );

    // Listen to tab controller changes and update the parent state
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        widget.selectedTabIndex.value = _tabController.index;
      }
    });

    // Listen to external tab index changes
    widget.selectedTabIndex.addListener(_onTabIndexChanged);
  }

  void _onTabIndexChanged() {
    if (_tabController.index != widget.selectedTabIndex.value) {
      _tabController.animateTo(widget.selectedTabIndex.value);
    }
  }

  void _initializeTabs() {
    final isChinese = RegExp(r'[\u4e00-\u9fa5]').hasMatch(widget.selectedText);

    if (isChinese) {
      _tabLabels.add('Dict');

      // Add Set/Word tab if meaningful segmentation is likely
      if (widget.selectedText.length > 2) {
        _tabLabels.add('Set');
      }

      // Add Char tab for multiple characters or polyphonic single characters
      final chineseChars = widget.selectedText
          .split('')
          .where((c) => RegExp(r'[\u4e00-\u9fa5]').hasMatch(c))
          .toList();
      if (chineseChars.length > 1 || _isLikelyPolyphonic(chineseChars.first)) {
        _tabLabels.add('Char'); // Changed from 'CHAR' to 'Char'
      }
    }
  }

  bool _isLikelyPolyphonic(String character) {
    const commonPolyphonicChars = {
      '没',
      '得',
      '的',
      '地',
      '着',
      '了',
      '中',
      '长',
      '大',
      '小',
      '好',
      '行',
      '发',
      '会',
      '要',
      '还',
      '都',
      '为',
      '和',
      '种',
      '重',
      '应',
      '便',
      '间',
      '传',
      '教',
      '数',
      '处',
      '调',
      '背',
      '当',
      '正',
      '几',
      '分',
      '切',
      '单',
      '干',
      '华',
      '结',
      '解',
      '空',
      '累',
      '量',
      '论',
      '难',
      '强',
      '任',
      '散',
      '少',
      '省',
      '识',
      '似',
      '提',
      '系',
      '相',
      '兴',
      '血',
      '压',
      '一',
      '音',
      '与',
      '语',
      '载',
      '占',
      '只',
      '转',
      '作',
      '做',
      '钻',
    };
    return commonPolyphonicChars.contains(character);
  }

  /// Get the appropriate icon for each tab
  IconData _getTabIcon(String tabLabel) {
    switch (tabLabel) {
      case 'Dict':
        return Icons.menu_book; // Dictionary/reference book icon
      case 'Set':
        return Icons.content_cut; // Segmentation/cutting icon
      case 'Char':
        return Icons.text_fields; // Character/text fields icon
      default:
        return Icons.help_outline; // Fallback icon
    }
  }

  /// Get the tooltip text for each tab
  String _getTabTooltip(String tabLabel) {
    switch (tabLabel) {
      case 'Dict':
        return 'Dictionary Lookup';
      case 'Set':
        return 'Word Segmentation';
      case 'Char':
        return 'Character Analysis';
      default:
        return tabLabel;
    }
  }

  @override
  void dispose() {
    widget.selectedTabIndex.removeListener(_onTabIndexChanged);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_tabLabels.isEmpty) return const SizedBox.shrink();

    final theme = Theme.of(context);

    // Use manufacturer-adjusted adaptive sizing for pixel-perfect consistency across all devices
    final mediaQuery = MediaQuery.of(context);
    final devicePixelRatio = mediaQuery.devicePixelRatio;
    final textScaler = mediaQuery.textScaler;

    // Base dimensions following the same pattern as ResponsiveTab
    final baseHeight = DesignSystem.isSmallPhone(context) ? 28.0 : 32.0;
    final baseIconSize =
        DesignSystem.isSmallPhone(context) ? AppIcons.sizeXS : AppIcons.sizeXS;

    // Apply device density adjustments (normalize to 3.0 DPI like ResponsiveTab)
    double densityFactor = (devicePixelRatio / 3.0).clamp(0.85, 1.25);

    // Apply text scaling with reasonable bounds
    double textScaleFactor = textScaler.scale(1.0).clamp(0.9, 1.4);

    // Apply manufacturer-specific adjustments for pixel-perfect consistency
    final spacingMultiplier =
        DesignSystem.getAdaptiveSpacingWithManufacturerAdjustment(context, 1.0);

    // Calculate final dimensions
    final adaptiveHeight = (baseHeight *
            densityFactor *
            textScaleFactor *
            spacingMultiplier)
        .clamp(DesignSystem.spaceL, DesignSystem.spaceXL + DesignSystem.spaceS);
    final adaptiveIconSize =
        DesignSystem.getAdjustedIconSize(baseIconSize * densityFactor);
    final adaptiveBorderRadius =
        adaptiveHeight / 2; // Half of height for perfect circle

    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: adaptiveHeight,
        minHeight: adaptiveHeight,
      ),
      child: Container(
        height: adaptiveHeight,
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.readingTextColor.withValues(alpha: 0.2),
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(adaptiveBorderRadius),
        ),
        child: TabBar(
          controller: _tabController,
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: widget.readingTextColor.withValues(alpha: 0.6),
          indicatorColor: theme.colorScheme.primary,
          indicatorWeight: DesignSystem.spaceTiny,
          indicatorSize: TabBarIndicatorSize.tab,
          dividerColor: Colors.transparent,
          labelStyle: theme.textTheme.labelSmall?.copyWith(
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
            fontSize: DesignSystem.isSmallPhone(context)
                ? DesignSystem.fontSizeXS
                : DesignSystem.fontSizeS,
          ),
          unselectedLabelStyle: theme.textTheme.labelSmall?.copyWith(
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.normal),
            fontSize: DesignSystem.isSmallPhone(context)
                ? DesignSystem.fontSizeXS
                : DesignSystem.fontSizeS,
          ),
          labelPadding:
              DesignSystem.getAdaptivePaddingWithManufacturerAdjustment(
            context,
            EdgeInsets.symmetric(
              horizontal:
                  DesignSystem.getContentDensitySpacing(ContentDensity.compact),
            ),
          ),
          tabs: _tabLabels
              .map(
                (label) => Tab(
                  child: Semantics(
                    label: _getTabTooltip(label),
                    hint: 'Tab for ${_getTabTooltip(label).toLowerCase()}',
                    button: true,
                    child: Tooltip(
                      message: _getTabTooltip(label),
                      child: Icon(
                        _getTabIcon(label),
                        size: adaptiveIconSize,
                      ),
                    ),
                  ),
                ),
              )
              .toList(),
        ),
      ),
    );
  }
}

/// A compact circular icon button for the combined row layout
class _CompactCircleIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onTap;
  final bool showColorIndicator;
  final String? colorValue;
  final String? semanticsLabel;
  final bool isCompact;

  const _CompactCircleIconButton({
    required this.icon,
    this.onTap,
    this.showColorIndicator = false,
    this.colorValue,
    this.semanticsLabel,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isEnabled = onTap != null;

    // Get reading theme colors
    final readingTextColor =
        Color(int.parse('0x${Prefs().readTheme.textColor}'));

    // WCAG AAA compliant touch targets with manufacturer adjustments for pixel-perfect consistency
    final baseButtonSize = isCompact
        ? DesignSystem.widgetMinTouchTarget // 44.0
        : DesignSystem.widgetMinTouchTarget + DesignSystem.spaceXS; // 48.0
    final baseIconSize = isCompact
        ? DesignSystem.spaceM + DesignSystem.spaceTiny // 18.0
        : DesignSystem.spaceM + DesignSystem.spaceXS; // 20.0

    // Apply manufacturer-specific adjustments for pixel-perfect consistency
    final buttonSize =
        baseButtonSize; // Touch targets should remain consistent for accessibility
    final iconSize = DesignSystem.getAdjustedIconSize(baseIconSize);

    // Primary color for the button
    final buttonColor = isEnabled
        ? colorScheme.primary
        : readingTextColor.withValues(
            alpha: DesignSystem.stateLayerDisabledOpacity,
          );

    return Tooltip(
      message: semanticsLabel ?? 'Button',
      child: SemanticHelpers.button(
        context: context,
        label: semanticsLabel ?? 'Button',
        onTap: onTap,
        enabled: isEnabled,
        child: Material(
          color: Colors.transparent,
          shape: const CircleBorder(),
          clipBehavior: Clip.antiAlias,
          child: InkWell(
            onTap: onTap,
            // MD3 state layers with standardized opacities
            hoverColor: DesignSystem.getHoverStateColor(buttonColor),
            focusColor: DesignSystem.getFocusStateColor(buttonColor),
            splashColor: DesignSystem.getPressedStateColor(buttonColor),
            highlightColor: DesignSystem.getStateLayerColor(
              buttonColor,
              DesignSystem.stateLayerPressedOpacity / 2,
            ),
            splashFactory: InkRipple.splashFactory,
            child: Ink(
              width: buttonSize,
              height: buttonSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isEnabled
                    ? Colors.transparent
                    : readingTextColor.withValues(alpha: 0.1),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Icon(
                    icon,
                    size: iconSize,
                    color: buttonColor,
                  ),
                  if (showColorIndicator && colorValue != null)
                    Positioned(
                      bottom: DesignSystem.spaceXS,
                      right: DesignSystem.spaceXS,
                      child: Container(
                        width: DesignSystem.spaceS,
                        height: DesignSystem.spaceS,
                        decoration: BoxDecoration(
                          color: Color(int.parse('0xFF$colorValue')),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Theme.of(context).colorScheme.surface,
                            width: DesignSystem.spaceMicro, // 1.0
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// A custom circular icon button widget for consistent styling
/// A Material Design 3 compliant circular icon button with state layers
class CircleIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onTap;
  final bool showColorIndicator;
  final String? colorValue;
  final String? semanticsLabel;

  const CircleIconButton({
    super.key,
    required this.icon,
    this.onTap,
    this.showColorIndicator = false,
    this.colorValue,
    this.semanticsLabel,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isEnabled = onTap != null;

    // Get reading theme colors
    final readingTextColor =
        Color(int.parse('0x${Prefs().readTheme.textColor}'));

    // WCAG AAA compliant touch targets - minimum 44dp
    final buttonSize = DesignSystem.isSmallPhone(context)
        ? DesignSystem.widgetMinTouchTarget
        : DesignSystem.widgetMinTouchTarget + DesignSystem.spaceXS;
    final baseIconSize =
        DesignSystem.isSmallPhone(context) ? AppIcons.sizeS : AppIcons.sizeS;
    final iconSize = DesignSystem.getAdjustedIconSize(baseIconSize);

    // Primary color for the button - using standardized colors
    final buttonColor = isEnabled
        ? colorScheme.primary
        : ColorSystem.getStateColor(UIState.disabled);

    return Tooltip(
      message: semanticsLabel ?? 'Button',
      child: SemanticHelpers.button(
        context: context,
        label: semanticsLabel ?? 'Button',
        onTap: onTap,
        enabled: isEnabled,
        child: Material(
          color: Colors.transparent,
          shape: const CircleBorder(),
          clipBehavior: Clip.antiAlias,
          child: InkWell(
            onTap: onTap,
            // MD3 state layers with standardized opacities
            hoverColor: DesignSystem.getHoverStateColor(buttonColor),
            focusColor: DesignSystem.getFocusStateColor(buttonColor),
            splashColor: DesignSystem.getPressedStateColor(buttonColor),
            highlightColor: DesignSystem.getStateLayerColor(
              buttonColor,
              DesignSystem.stateLayerPressedOpacity / 2,
            ),
            // MD3 motion system timing
            splashFactory: InkRipple.splashFactory,
            child: Ink(
              width: buttonSize,
              height: buttonSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isEnabled
                    ? Colors.transparent
                    : DesignSystem.getStateLayerColor(readingTextColor, 0.1),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Icon(
                    icon,
                    size: iconSize,
                    color: buttonColor,
                  ),
                  if (showColorIndicator && colorValue != null)
                    Positioned(
                      bottom: DesignSystem.spaceXS,
                      right: DesignSystem.spaceXS,
                      child: Container(
                        width: DesignSystem.spaceS,
                        height: DesignSystem.spaceS,
                        decoration: BoxDecoration(
                          color: Color(int.parse('0xFF$colorValue')),
                          shape: BoxShape.circle,
                          // Simple border to show color indicator
                          border: Border.all(
                            color: Theme.of(context).colorScheme.surface,
                            width: DesignSystem.spaceMicro,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
